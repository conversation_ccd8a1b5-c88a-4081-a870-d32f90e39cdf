import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ref, nextTick } from 'vue';
import { createTimeManager } from '../create-time-manager';
import { TimerStatus } from '../types';
import { useTimer } from '../use-timer';

const raf = vi.fn((callback: FrameRequestCallback) => {
    const id = setTimeout(() => callback(performance.now()), 16);
    return id as unknown as number;
});

const caf = vi.fn((id: number) => {
    clearTimeout(id);
});

describe('异步操作复杂场景测试', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        raf.mockClear();
        caf.mockClear();

        // 简化 performance.now() 模拟，基于 vitest 假时间系统
        const startTime = 100000; // 初始基准时间
        let baseTime = Date.now();

        vi.spyOn(performance, 'now').mockImplementation(() => {
            const currentTime = Date.now();
            return startTime + (currentTime - baseTime);
        });
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('复杂同步场景测试', () => {
        it('应该处理同步函数返回慢速Promise的情况', async () => {
            let resolveSync: (value: number) => void;
            const slowSyncPromise = new Promise<number>((resolve) => {
                resolveSync = resolve;
            });

            const mockSyncTime = vi.fn().mockReturnValue(slowSyncPromise);

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            // 推进时间以触发同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(mockSyncTime).toHaveBeenCalled();
            expect(timer.status).toBe(TimerStatus.running);

            // 慢速同步完成
            resolveSync!(2000000);
            await nextTick();

            expect(timeCenter.currentTime.value).toBeGreaterThanOrEqual(2000000);

            timeCenter.stopUpdateTime();
        });

        it('应该处理同步请求超时的情况', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            const mockSyncTime = vi.fn().mockImplementation(() => {
                return new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Timeout')), 5000);
                });
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            // 推进时间触发同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            // 推进时间使超时发生，并等待 promise reject/resolve
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器时间同步失败:', expect.any(Error));
            expect(timer.status).toBe(TimerStatus.running); // 应该继续正常工作

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该处理同步过程中时间中心被停止的情况', async () => {
            let resolveSync: (value: number) => void;
            const syncPromise = new Promise<number>((resolve) => {
                resolveSync = resolve;
            });

            const mockSyncTime = vi.fn().mockReturnValue(syncPromise);

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            // 触发同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            // 在同步完成前停止时间中心
            timeCenter.stopUpdateTime();

            // 完成同步
            resolveSync!(2000000);
            await nextTick();

            // 验证停止后不再更新
            expect(caf).toHaveBeenCalled();
        });
    });

    describe('并发异步操作测试', () => {
        it('应该处理多个同步请求并发的情况', async () => {
            let syncCallCount = 0;
            const mockSyncTime = vi.fn().mockImplementation(() => {
                syncCallCount++;
                return Promise.resolve(Date.now() + syncCallCount * 1000);
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 100, // 快速触发多次同步
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            // 快速触发多次同步
            for (let i = 0; i < 5; i++) {
                await vi.advanceTimersToNextTimerAsync();
                await nextTick();
            }

            expect(mockSyncTime).toHaveBeenCalled();
            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
        });

        it('应该处理异步回调中的状态变更', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer = createTimer();

            let asyncCallbackExecuted = false;
            const asyncCallback = vi.fn().mockImplementation(async () => {
                await new Promise((resolve) => setTimeout(resolve, 10));
                asyncCallbackExecuted = true;
                timer.pause(); // 在异步回调中改变状态
            });

            timer.start({
                onTick: asyncCallback,
            });

            // 等待异步回调完成
            await vi.advanceTimersToNextTimerAsync(); // for requestAnimationFrame
            await nextTick();
            await vi.advanceTimersToNextTimerAsync(); // for setTimeout in callback
            await nextTick();
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            expect(asyncCallbackExecuted).toBe(true);
            expect(timer.status).toBe(TimerStatus.suspended);
            timeCenter.stopUpdateTime();
        });
    });

    describe('Promise链和错误传播测试', () => {
        it('应该正确处理Promise链中的错误', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            const mockSyncTime = vi.fn().mockImplementation(() => {
                return Promise.resolve(Date.now()).then(() => {
                    throw new Error('Chain error');
                });
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器时间同步失败:', expect.any(Error));

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该处理async/await语法的同步函数', async () => {
            const mockSyncTime = vi.fn().mockImplementation(async () => {
                await new Promise((resolve) => setTimeout(resolve, 10));
                return Date.now() + 5000;
            });

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(mockSyncTime).toHaveBeenCalled();
            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
        });
    });

    describe('资源清理和异步操作测试', () => {
        it('应该在计时器销毁时取消正在进行的异步操作', async () => {
            const { createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer = createTimer();

            // let asyncOperationCancelled = false;
            const longRunningCallback = vi.fn().mockImplementation(async () => {
                try {
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                } catch (error) {
                    asyncOperationCancelled = true;
                }
            });

            timer.start({
                onTick: longRunningCallback,
            });

            // 立即销毁计时器
            timer.destroy();

            expect(timer.status).toBe(TimerStatus.destroyed);
            expect(timer.onTick).toBeUndefined();

            // 即使有长时间运行的异步操作，销毁也应该立即生效
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();
        });

        it('应该处理销毁过程中的异步清理', async () => {
            const { createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timers = Array.from({ length: 10 }, () => createTimer());

            // 启动所有计时器
            timers.forEach((timer) => {
                timer.start({
                    onTick: async () => {
                        await new Promise((resolve) => setTimeout(resolve, 1));
                    },
                });
            });

            // 并发销毁所有计时器
            const destroyPromises = timers.map((timer) => Promise.resolve().then(() => timer.destroy()));

            await Promise.all(destroyPromises);

            // 验证所有计时器都被正确销毁
            timers.forEach((timer) => {
                expect(timer.status).toBe(TimerStatus.destroyed);
            });
        });
    });

    describe('时区和环境变化测试', () => {
        it('应该处理Date.now()和performance.now()的差异', async () => {
            // 模拟Date.now()和performance.now()返回不同基准的时间
            const originalDateNow = Date.now;
            const originalPerfNow = performance.now;

            Date.now = vi.fn().mockReturnValue(1000000000); // Unix timestamp
            performance.now = vi.fn().mockReturnValue(500000); // Performance timestamp

            const { createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer = createTimer();

            timer.start({});

            expect(timer.status).toBe(TimerStatus.running);

            // 恢复原始函数
            Date.now = originalDateNow;
            performance.now = originalPerfNow;
        });

        it('应该处理系统时间跳跃的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 5000,
            });

            // 模拟系统时间向前跳跃
            mockCurrentTime.value += 10000; // 跳跃10秒
            await nextTick();

            // 计时器应该正确处理时间跳跃
            expect(timer.status).toBe(TimerStatus.stopped); // 应该已经结束
        });

        it('应该处理系统时间向后跳跃的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            // 模拟系统时间向后跳跃
            mockCurrentTime.value -= 5000;
            await nextTick();

            // 应该显示负的经过时间
            expect(timer.elapsedTime.total).toBeLessThan(0);
            expect(timer.status).toBe(TimerStatus.running); // 但仍然运行
        });

        it('应该不受时间基准变化影响', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer = createTimer();

            timer.start({
                beginTime: () => timeCenter.currentTime.value,
            });

            // 推进30秒
            await vi.advanceTimersByTimeAsync(30 * 1000);
            await nextTick();

            const elapsedBeforeJump = timer.elapsedTime.total;
            expect(elapsedBeforeJump).toBeCloseTo(30000, -2);

            // 再推进30秒，验证时间累积的一致性
            await vi.advanceTimersByTimeAsync(30 * 1000);
            await nextTick();

            // 总共应该是60秒
            expect(timer.elapsedTime.total).toBeCloseTo(60000, -2);
            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
        });

        it('应该基于内部时间而非系统时间工作', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timer = createTimer();

            timer.start({
                beginTime: () => timeCenter.currentTime.value,
                finishTime: () => timeCenter.currentTime.value + 10000, // 10s timer
            });

            // 推进1秒
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();
            const elapsedAfterFirstSecond = timer.elapsedTime.total;
            expect(elapsedAfterFirstSecond).toBeCloseTo(1000, -2);

            // 再推进1秒
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            // 应该累积到2秒
            expect(timer.elapsedTime.total).toBeCloseTo(2000, -2);
            expect(timer.status).toBe(TimerStatus.running);

            // 剩余时间应该是8秒
            expect(timer.remainingTime.total).toBeCloseTo(8000, -2);

            timeCenter.stopUpdateTime();
        });
    });
});
