<template>
    <Story>
        <div class="container">
            <div class="list">
                <div class="group-title">
                    people-1 group
                </div>
                <DraggableList v-model="list_1" :options="options_1" class="drag-area">
                    <template #item="{ item, index }">
                        <div class="item">
                            <div>{{ item.name }}</div>
                            <div>index: {{ index }}</div>
                        </div>
                    </template>
                </DraggableList>
            </div>

            <div class="list">
                <div class="group-title">
                    people-1 group
                </div>
                <DraggableList v-model="list_2" :options="options_1" class="drag-area">
                    <template #item="{ item, index }">
                        <div class="item">
                            <div>{{ item.name }}</div>
                            <div>index: {{ index }}</div>
                        </div>
                    </template>
                </DraggableList>
            </div>

            <div class="list">
                <div class="group-title">
                    people-2 group
                </div>
                <DraggableList v-model="list_3" :options="options_2" class="drag-area">
                    <template #item="{ item, index }">
                        <div class="item">
                            <div>{{ item.name }}</div>
                            <div>index: {{ index }}</div>
                        </div>
                    </template>
                </DraggableList>
            </div>
        </div>
    </Story>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DraggableList } from '.'
import { getUUID } from '@crm/exam-utils'

// 生成随机名字的工具函数
function generateRandomName() {
    const firstNames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴'];
    const lastNames = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋'];
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    return firstName + lastName;
}

const list_1 = ref(Array.from({ length: 10 }, () => ({ name: generateRandomName(), id: getUUID() })))
const list_2 = ref(Array.from({ length: 10 }, () => ({ name: generateRandomName(), id: getUUID() })))
const list_3 = ref(Array.from({ length: 10 }, () => ({ name: generateRandomName(), id: getUUID() })))

const options_1 = ref({ group: 'people-1' })
const options_2 = ref({ group: 'people-2' })
</script>

<style scoped>
.list {
    padding: 16px;
    list-style: none;
    margin: 0;
    width: 100%;
    max-height: 350px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
    background: #fff;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    user-select: none;
}
.item {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #666;
    cursor: move;
}
.container {
    display: flex;
    flex-direction: row;
    gap: 10px;
}
.drag-area {
    overflow-y: auto;
    width: 100%;
}
.group-title {
    position: sticky;
    top: 0;
    background-color: #fff;
}
</style>

<docs lang="md">
## DraggableList 可拖拽列表

一个可以拖放排序的列表组件，使用 vue-draggable-plus 实现。

-   使用 v-model 传入列表数据，注意必须包含 id 字段
-   options 属性参考 [vue-draggable-plus API 文档](https://vue-draggable-plus.pages.dev/api/#options)
</docs>
